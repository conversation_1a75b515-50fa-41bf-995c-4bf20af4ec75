"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_browsing_BrowsingDetectionService_ts";
exports.ids = ["_rsc_src_lib_browsing_BrowsingDetectionService_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/browsing/BrowsingDetectionService.ts":
/*!******************************************************!*\
  !*** ./src/lib/browsing/BrowsingDetectionService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowsingDetectionService: () => (/* binding */ BrowsingDetectionService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Browsing Detection Service - Determines when user queries require web browsing\n// Similar to RAG detection but for real-time web information needs\nclass BrowsingDetectionService {\n    constructor(){\n        this.classificationApiKey = null;\n        this.classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY || null;\n    }\n    static getInstance() {\n        if (!BrowsingDetectionService.instance) {\n            BrowsingDetectionService.instance = new BrowsingDetectionService();\n        }\n        return BrowsingDetectionService.instance;\n    }\n    /**\n   * Determine if a user query requires web browsing for real-time information\n   * TEMPORARILY DISABLED: Always returns false to disable browsing functionality\n   */ async shouldTriggerBrowsing(query, conversationContext) {\n        // TEMPORARY: Browsing functionality disabled for launch\n        // Set ROKEY_ENABLE_BROWSING=true in environment to re-enable\n        const browsingEnabled = process.env.ROKEY_ENABLE_BROWSING === 'true';\n        if (!browsingEnabled) {\n            console.log(`[Browsing Detection] Browsing temporarily disabled for query: \"${query.substring(0, 50)}...\"`);\n            return {\n                shouldBrowse: false,\n                confidence: 1.0\n            };\n        }\n        try {\n            // Skip very short queries immediately\n            if (query.trim().length < 8) {\n                return {\n                    shouldBrowse: false\n                };\n            }\n            if (!this.classificationApiKey) {\n                console.warn('[Browsing Detection] No classification API key, using fallback logic');\n                return this.fallbackDetection(query);\n            }\n            const classificationPrompt = `You are an intelligent browsing classifier. Analyze if this user query requires real-time web browsing to get current information.\n\nUSER QUERY: \"${query}\"\n\n${conversationContext ? `CONVERSATION CONTEXT: ${conversationContext}` : ''}\n\nCLASSIFICATION CRITERIA:\n✅ REQUIRES BROWSING (YES) when:\n- Query asks for CURRENT/RECENT information (news, events, prices, weather, etc.)\n- Query mentions specific dates, \"today\", \"now\", \"latest\", \"current\", \"recent\"\n- Query asks about live data (stock prices, exchange rates, sports scores)\n- Query asks about current events, breaking news, or recent developments\n- Query asks about website content, specific URLs, or online resources\n- Query asks \"what's happening\", \"latest news about\", \"current status of\"\n- Query asks about real-time information that changes frequently\n- Query asks about specific companies' current status, recent announcements\n- Query asks about weather, traffic, or location-based current information\n- Query asks about availability, pricing, or current offers from websites\n- Query asks about recent social media posts, trends, or viral content\n- Query asks about current job listings, real estate, or market data\n- Query asks about live events, schedules, or current availability\n\n❌ DOESN'T REQUIRE BROWSING (NO) when:\n- General knowledge questions about historical facts, definitions, concepts\n- Questions about well-established information that doesn't change\n- Creative writing, coding help, math problems, or educational content\n- Personal advice, opinions, or subjective questions\n- Questions about basic facts, scientific principles, or common knowledge\n- Simple greetings, thanks, or conversational responses\n- Questions that can be answered with existing AI knowledge\n- Questions about famous people's basic biographical information\n- Questions about established theories, concepts, or principles\n\nEXAMPLES:\n- \"What's the current price of Bitcoin?\" → YES|SEARCH|current Bitcoin price USD value today\n- \"What's happening in Ukraine today?\" → YES|SEARCH|Ukraine news today current events latest developments\n- \"Check the weather in New York\" → YES|SEARCH|New York weather today current forecast\n- \"What is machine learning?\" → NO (general knowledge)\n- \"Help me write a poem\" → NO (creative task)\n- \"What's 2+2?\" → NO (basic math)\n\nRESPONSE FORMAT:\nIf YES: \"YES|BROWSING_TYPE|refined_search_query\"\nIf NO: \"NO\"\n\nBROWSING_TYPE options:\n- SEARCH: For general web search queries\n- NAVIGATE: For specific website/URL content\n- EXTRACT: For extracting specific data from known sources\n\nSEARCH QUERY REFINEMENT RULES:\n- Remove conversational words like \"tell me\", \"what's\", \"please\", etc.\n- Focus on key terms and current/recent indicators\n- Include time-sensitive terms like \"today\", \"current\", \"latest\", \"recent\"\n- Make queries specific and search-engine friendly`;\n            const classificationPayload = {\n                model: 'gemini-2.0-flash-001',\n                messages: [\n                    {\n                        role: 'user',\n                        content: classificationPrompt\n                    }\n                ],\n                temperature: 0.1,\n                max_tokens: 150\n            };\n            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${this.classificationApiKey}`\n                },\n                body: JSON.stringify(classificationPayload),\n                signal: AbortSignal.timeout(3000) // Quick 3s timeout for classification\n            });\n            if (!response.ok) {\n                console.warn('[Browsing Detection] Classification failed, using fallback');\n                return this.fallbackDetection(query);\n            }\n            const result = await response.json();\n            const decision = result.choices?.[0]?.message?.content?.trim();\n            if (decision?.startsWith('YES|')) {\n                const parts = decision.substring(4).split('|');\n                const browsingType = parts[0]?.toLowerCase();\n                const refinedQuery = parts[1]?.trim();\n                console.log(`[Browsing Detection] Gemini decision for \"${query.substring(0, 50)}...\": YES (BROWSE) - Type: ${browsingType}, Query: \"${refinedQuery}\"`);\n                return {\n                    shouldBrowse: true,\n                    refinedQuery: refinedQuery || query,\n                    browsingType: browsingType || 'search',\n                    confidence: 0.9\n                };\n            } else if (decision?.startsWith('NO')) {\n                console.log(`[Browsing Detection] Gemini decision for \"${query.substring(0, 50)}...\": NO (SKIP) - AI can handle this`);\n                return {\n                    shouldBrowse: false,\n                    confidence: 0.9\n                };\n            } else {\n                // Fallback for unexpected response format\n                console.warn(`[Browsing Detection] Unexpected decision format: \"${decision}\", using fallback`);\n                return this.fallbackDetection(query);\n            }\n        } catch (error) {\n            console.warn('[Browsing Detection] Classification error, using fallback:', error);\n            return this.fallbackDetection(query);\n        }\n    }\n    /**\n   * Fallback detection logic when AI classification is unavailable\n   */ fallbackDetection(query) {\n        const lowerQuery = query.toLowerCase();\n        // Time-sensitive keywords\n        const timeKeywords = [\n            'current',\n            'today',\n            'now',\n            'latest',\n            'recent',\n            'live',\n            'real-time',\n            'breaking'\n        ];\n        const hasTimeKeywords = timeKeywords.some((keyword)=>lowerQuery.includes(keyword));\n        // Information type keywords\n        const infoKeywords = [\n            'news',\n            'price',\n            'weather',\n            'stock',\n            'rate',\n            'score',\n            'status',\n            'update'\n        ];\n        const hasInfoKeywords = infoKeywords.some((keyword)=>lowerQuery.includes(keyword));\n        // Question patterns that typically need browsing\n        const browsingPatterns = [\n            /what'?s happening/i,\n            /what'?s the (current|latest)/i,\n            /check the/i,\n            /find out/i,\n            /look up/i\n        ];\n        const matchesPattern = browsingPatterns.some((pattern)=>pattern.test(query));\n        const shouldBrowse = hasTimeKeywords || hasInfoKeywords || matchesPattern;\n        if (shouldBrowse) {\n            console.log(`[Browsing Detection] Fallback decision for \"${query.substring(0, 50)}...\": YES (BROWSE) - Pattern match`);\n            return {\n                shouldBrowse: true,\n                refinedQuery: query,\n                browsingType: 'search',\n                confidence: 0.6\n            };\n        } else {\n            console.log(`[Browsing Detection] Fallback decision for \"${query.substring(0, 50)}...\": NO (SKIP) - No browsing patterns`);\n            return {\n                shouldBrowse: false,\n                confidence: 0.6\n            };\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BrowsingDetectionService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/browsing/BrowsingDetectionService.ts\n");

/***/ })

};
;